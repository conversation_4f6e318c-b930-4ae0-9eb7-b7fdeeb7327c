{"version": "6", "dialect": "sqlite", "id": "e55256c4-5f9c-4674-b732-31c4033b7f7e", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"fulfilled_orders": {"name": "fulfilled_orders", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_order_id": {"name": "provider_order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "shopify_order_number": {"name": "shopify_order_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "shopify_order_id": {"name": "shopify_order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fulfilled_at": {"name": "fulfilled_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"idx_provider_order": {"name": "idx_provider_order", "columns": ["provider", "provider_order_id"], "isUnique": false}, "idx_shopify_order": {"name": "idx_shopify_order", "columns": ["shopify_order_number"], "isUnique": false}, "provider_order_unique": {"name": "provider_order_unique", "columns": ["provider", "provider_order_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}