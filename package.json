{"name": "laplace-fulfiller", "module": "index.ts", "type": "module", "private": true, "scripts": {"graphql-codegen": "graphql-codegen", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@shopify/shopify-api": "^11.14.1", "croner": "^9.1.0", "drizzle-orm": "^0.44.4"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.6.2", "@shopify/api-codegen-preset": "^1.1.9", "@types/bun": "^1.2.20", "drizzle-kit": "^0.31.4", "prettier": "^3.6.2"}, "peerDependencies": {"typescript": "^5.9.2"}}